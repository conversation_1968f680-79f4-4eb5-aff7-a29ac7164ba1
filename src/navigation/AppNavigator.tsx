import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// 全局导航类型声明
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

// 导入页面
import HomeScreen from '../screens/HomeScreen';
import DetailScreen from '../screens/DetailScreen';
import ProfileScreen from '../screens/ProfileScreen';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// 定义导航栈类型（TypeScript 推荐）
export type RootStackParamList = {
  Home: undefined;
  Detail: undefined;
  Profile: undefined;
  
};

// 创建Stack导航栈
const Stack = createNativeStackNavigator<RootStackParamList>();
// 创建BottomTab导航栈
const Tab = createBottomTabNavigator<RootStackParamList>();

// 定义底部标签页的导航器
function HomeTabs() {
  return (
    <Tab.Navigator
      screenOptions={{
        // 可以在这里统一设置所有 Tab 的样式
        headerShown: false, // 如果你希望每个 Tab 内部有独立的 header，可以在这里关闭
        tabBarActiveTintColor: '#1a73e8', // 选中标签的文字颜色
        tabBarInactiveTintColor: 'gray',  // 未选中标签的文字颜色
        tabBarStyle: {
          backgroundColor: '#fff', // 标签栏背景色
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
        },
        tabBarItemStyle: {
          paddingVertical: 6,
        },
      }}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{ 
          title: '首页',
          // 可以在这里为单个 Tab 设置图标（需要配合 react-native-vector-icons 等库）
          // tabBarIcon: ({ color, size }) => (
          //   <Ionicons name="home" color={color} size={size} />
          // ),
        }} 
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{ 
          title: '我的' 
        }} 
      />
    </Tab.Navigator>
  );
}


const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#1a73e8',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen 
          name="MainTabs" 
          component={HomeTabs} 
          options={{ headerShown: false }} 
        />
        <Stack.Screen
          name="Detail"
          component={DetailScreen}
          options={{ title: '详情' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
